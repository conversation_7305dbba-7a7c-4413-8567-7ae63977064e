import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response } from 'express';
import { outletController } from '../outlet.controller';
import { outletService } from '../../../business/services/outlet.service';
import { CreateOutletPayload, UpdateOutletPayload } from '../../../data/models/outlet.model';

// Mock the outlet service
vi.mock('../../../business/services/outlet.service', () => ({
  outletService: {
    getAllOutlets: vi.fn(),
    getOutletById: vi.fn(),
    createOutlet: vi.fn(),
    updateOutlet: vi.fn(),
    deleteOutlet: vi.fn(),
    restoreOutlet: vi.fn(),
    bulkDeleteOutlets: vi.fn(),
    activateOutlet: vi.fn(),
    deactivateOutlet: vi.fn(),
    getOutletStatsSummary: vi.fn(),
    searchOutlets: vi.fn(),
  },
}));

// Mock logger
vi.mock('../../../infrastructure/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('OutletController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: vi.Mock;

  beforeEach(() => {
    vi.clearAllMocks();

    mockRequest = {
      body: {},
      params: {},
      query: {},
      user: { userId: 1, role: 'admin' },
    };

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };

    mockNext = vi.fn();
  });

  describe('getAll', () => {
    it('should return all outlets with pagination', async () => {
      const mockOutlets = {
        outlets: [
          {
            outletId: 1,
            name: 'Test Outlet 1',
            address: '123 Test St',
            phoneNumber: '+1234567890',
            isActive: true,
          },
          {
            outletId: 2,
            name: 'Test Outlet 2',
            address: '456 Test Ave',
            phoneNumber: '+0987654321',
            isActive: true,
          },
        ],
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      vi.mocked(outletService.getAllOutlets).mockResolvedValue(mockOutlets);

      mockRequest.query = { page: '1', limit: '10' };

      await outletController.getAll(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.getAllOutlets).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlets retrieved successfully',
        data: mockOutlets,
      });
    });

    it('should handle service errors', async () => {
      const error = new Error('Database connection failed');
      vi.mocked(outletService.getAllOutlets).mockRejectedValue(error);

      await outletController.getAll(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should apply filters when provided', async () => {
      const mockOutlets = {
        outlets: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      vi.mocked(outletService.getAllOutlets).mockResolvedValue(mockOutlets);

      mockRequest.query = {
        page: '1',
        limit: '10',
        search: 'test',
        isActive: 'true',
        managerId: '1',
      };

      await outletController.getAll(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.getAllOutlets).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        search: 'test',
        isActive: true,
        managerId: 1,
      });
    });
  });

  describe('getById', () => {
    it('should return outlet by id', async () => {
      const mockOutlet = {
        outletId: 1,
        name: 'Test Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: true,
      };

      vi.mocked(outletService.getOutletById).mockResolvedValue(mockOutlet);

      mockRequest.params = { id: '1' };

      await outletController.getById(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.getOutletById).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet retrieved successfully',
        data: mockOutlet,
      });
    });

    it('should handle outlet not found', async () => {
      vi.mocked(outletService.getOutletById).mockResolvedValue(null);

      mockRequest.params = { id: '999' };

      await outletController.getById(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Outlet not found',
      });
    });
  });

  describe('create', () => {
    it('should create a new outlet', async () => {
      const createData: CreateOutletPayload = {
        name: 'New Outlet',
        address: '789 New St',
        phoneNumber: '+1111111111',
        email: '<EMAIL>',
        managerId: 1,
      };

      const mockCreatedOutlet = {
        outletId: 3,
        ...createData,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(outletService.createOutlet).mockResolvedValue(mockCreatedOutlet);

      mockRequest.body = createData;

      await outletController.create(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.createOutlet).toHaveBeenCalledWith(createData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet created successfully',
        data: mockCreatedOutlet,
      });
    });

    it('should handle validation errors', async () => {
      const error = new Error('Validation failed');
      vi.mocked(outletService.createOutlet).mockRejectedValue(error);

      mockRequest.body = { name: '' }; // Invalid data

      await outletController.create(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('update', () => {
    it('should update an existing outlet', async () => {
      const updateData: UpdateOutletPayload = {
        name: 'Updated Outlet',
        isActive: false,
      };

      const mockUpdatedOutlet = {
        outletId: 1,
        name: 'Updated Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: false,
        updatedAt: new Date(),
      };

      vi.mocked(outletService.updateOutlet).mockResolvedValue(mockUpdatedOutlet);

      mockRequest.params = { id: '1' };
      mockRequest.body = updateData;

      await outletController.update(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.updateOutlet).toHaveBeenCalledWith(1, updateData);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet updated successfully',
        data: mockUpdatedOutlet,
      });
    });

    it('should handle outlet not found during update', async () => {
      vi.mocked(outletService.updateOutlet).mockResolvedValue(null);

      mockRequest.params = { id: '999' };
      mockRequest.body = { name: 'Updated Name' };

      await outletController.update(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Outlet not found',
      });
    });
  });

  describe('delete', () => {
    it('should soft delete an outlet', async () => {
      vi.mocked(outletService.deleteOutlet).mockResolvedValue(true);

      mockRequest.params = { id: '1' };

      await outletController.delete(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.deleteOutlet).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet deleted successfully',
      });
    });

    it('should handle outlet not found during delete', async () => {
      vi.mocked(outletService.deleteOutlet).mockResolvedValue(false);

      mockRequest.params = { id: '999' };

      await outletController.delete(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Outlet not found',
      });
    });
  });

  describe('restore', () => {
    it('should restore a soft-deleted outlet', async () => {
      const mockRestoredOutlet = {
        outletId: 1,
        name: 'Restored Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: true,
        deletedAt: null,
      };

      vi.mocked(outletService.restoreOutlet).mockResolvedValue(mockRestoredOutlet);

      mockRequest.params = { id: '1' };

      await outletController.restore(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.restoreOutlet).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet restored successfully',
        data: mockRestoredOutlet,
      });
    });
  });

  describe('bulkAction', () => {
    it('should handle bulk delete action', async () => {
      const deletedCount = 3;
      vi.mocked(outletService.bulkDeleteOutlets).mockResolvedValue(deletedCount);

      mockRequest.body = {
        action: 'delete',
        outletIds: [1, 2, 3],
      };

      await outletController.bulkAction(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.bulkDeleteOutlets).toHaveBeenCalledWith([1, 2, 3]);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${deletedCount} outlets deleted successfully`,
        data: { affectedCount: deletedCount },
      });
    });

    it('should handle invalid bulk action', async () => {
      mockRequest.body = {
        action: 'invalid_action',
        outletIds: [1, 2, 3],
      };

      await outletController.bulkAction(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid bulk action',
      });
    });
  });

  describe('getStatsSummary', () => {
    it('should return outlet statistics summary', async () => {
      const mockStats = {
        totalOutlets: 10,
        activeOutlets: 8,
        inactiveOutlets: 2,
        totalRevenue: 50000,
        averageRevenue: 5000,
      };

      vi.mocked(outletService.getOutletStatsSummary).mockResolvedValue(mockStats);

      await outletController.getStatsSummary(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(outletService.getOutletStatsSummary).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Outlet statistics retrieved successfully',
        data: mockStats,
      });
    });
  });
});
