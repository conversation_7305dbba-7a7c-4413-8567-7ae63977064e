import { Request, Response, NextFunction } from 'express';
import { outletService, type OutletService } from '../../business/services/outlet.service';
import {
  type CreateOutletRequest,
  type UpdateOutletRequest,
  type OutletQuery,
  type BulkOutletAction,
  type OutletStatus,
} from '../../business/validators/outlet.validator';
// import { apiLogger } from '../../infrastructure/logger/pino';

export class OutletController {
  constructor(private readonly outletSvc: OutletService = outletService) { }

  getAll = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const query: OutletQuery = req.query as any;
      const result = await this.outletSvc.getAll(query);
      res.status(200).json({
        success: true,
        message: 'Outlets retrieved successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  getById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      const outlet = await this.outletSvc.getById(outletId);
      res.status(200).json({
        success: true,
        message: 'Outlet retrieved successfully',
        data: { outlet },
      });
    } catch (error) {
      next(error);
    }
  };

  create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletData: CreateOutletRequest = req.body;
      const outlet = await this.outletSvc.create(outletData);
      res.status(201).json({
        success: true,
        message: 'Outlet created successfully',
        data: { outlet },
      });
    } catch (error) {
      next(error);
    }
  };

  update = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      const updateData: UpdateOutletRequest = req.body;
      const updatedOutlet = await this.outletSvc.update(outletId, updateData);
      res.status(200).json({
        success: true,
        message: 'Outlet updated successfully',
        data: { outlet: updatedOutlet },
      });
    } catch (error) {
      next(error);
    }
  };

  delete = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      await this.outletSvc.delete(outletId);
      res.status(200).json({
        success: true,
        message: 'Outlet deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  restore = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      await this.outletSvc.restore(outletId);
      res.status(200).json({
        success: true,
        message: 'Outlet restored successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  updateStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      const { isActive }: OutletStatus = req.body;
      const updatedOutlet = await this.outletSvc.updateStatus(outletId, { isActive });
      res.status(200).json({
        success: true,
        message: 'Outlet status updated successfully',
        data: { outlet: updatedOutlet },
      });
    } catch (error) {
      next(error);
    }
  };

  getActive = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const activeOutlets = await this.outletSvc.getActive();
      res.status(200).json({
        success: true,
        message: 'Active outlets retrieved successfully',
        data: { outlets: activeOutlets },
      });
    } catch (error) {
      next(error);
    }
  };

  search = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const query: OutletQuery = req.query as any;
      const result = await this.outletSvc.getAll(query);
      res.status(200).json({
        success: true,
        message: 'Outlets search completed successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  getStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.outletSvc.getStats();
      res.status(200).json({
        success: true,
        message: 'Outlet stats retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  bulkAction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { action, outletIds }: BulkOutletAction = req.body;
      const result = await this.outletSvc.bulkAction(action, outletIds);
      res.status(200).json({
        success: true,
        message: result.message,
        data: { results: result.results },
      });
    } catch (error) {
      next(error);
    }
  };
}

export const outletController = new OutletController();
