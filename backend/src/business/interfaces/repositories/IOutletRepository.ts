// Repository interface abstraction for database-agnostic outlet operations
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface OutletQueryParams {
  isActive?: boolean;
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  minRating?: number;
  maxPrice?: number;
  cuisine?: string;
}

export interface CreateOutletData {
  name: string;
  address: string;
  phone?: string;
  description?: string;
  isActive?: boolean;
}

export interface UpdateOutletData {
  name?: string;
  address?: string;
  phone?: string;
  description?: string;
  isActive?: boolean;
}

// Database-agnostic outlet repository interface
export interface IOutletRepository {
  // Query operations
  findAll(query: OutletQueryParams): Promise<PaginatedResult<Outlet>>;
  findById(id: number): Promise<Outlet | null>;
  findByName(name: string): Promise<Outlet | null>;
  findAllActive(): Promise<ActiveOutlet[]>;
  getStats(): Promise<any>;
  
  // Command operations
  create(outletData: CreateOutletData): Promise<Outlet>;
  update(id: number, data: Partial<UpdateOutletData>): Promise<Outlet | null>;
  softDelete(id: number): Promise<boolean>;
  restore(id: number): Promise<boolean>;
  
  // Utility operations
  nameExists(name: string, excludeId?: number): Promise<boolean>;
}

// Domain models (separate from database entities)
export interface Outlet {
  outletId: number;
  name: string;
  address: string;
  phone: string | null;
  description: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface ActiveOutlet {
  outletId: number;
  name: string;
  address: string;
  isActive: boolean;
}
