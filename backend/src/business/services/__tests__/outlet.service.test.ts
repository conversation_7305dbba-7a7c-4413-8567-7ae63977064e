import { describe, it, expect, beforeEach, vi } from 'vitest';
import { outletService } from '../outlet.service';
import { outletRepository } from '../../../data/repositories/outlet.repository';
import { CreateOutletPayload, UpdateOutletPayload } from '../../../data/models/outlet.model';

// Mock the outlet repository
vi.mock('../../../data/repositories/outlet.repository', () => ({
  outletRepository: {
    findAll: vi.fn(),
    findById: vi.fn(),
    findByIdWithManager: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    restore: vi.fn(),
    bulkDelete: vi.fn(),
    activate: vi.fn(),
    deactivate: vi.fn(),
    getStatsSummary: vi.fn(),
    search: vi.fn(),
    count: vi.fn(),
  },
}));

// Mock logger
vi.mock('../../../infrastructure/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('OutletService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getAllOutlets', () => {
    it('should return paginated outlets with default parameters', async () => {
      const mockOutlets = [
        {
          outletId: 1,
          name: 'Test Outlet 1',
          address: '123 Test St',
          phoneNumber: '+1234567890',
          isActive: true,
        },
        {
          outletId: 2,
          name: 'Test Outlet 2',
          address: '456 Test Ave',
          phoneNumber: '+0987654321',
          isActive: true,
        },
      ];

      const mockTotal = 2;

      vi.mocked(outletRepository.findAll).mockResolvedValue(mockOutlets);
      vi.mocked(outletRepository.count).mockResolvedValue(mockTotal);

      const result = await outletService.getAllOutlets();

      expect(outletRepository.findAll).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
      });
      expect(outletRepository.count).toHaveBeenCalledWith({});
      expect(result).toEqual({
        outlets: mockOutlets,
        total: mockTotal,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should apply filters when provided', async () => {
      const filters = {
        search: 'test',
        isActive: true,
        managerId: 1,
        page: 2,
        limit: 5,
      };

      const mockOutlets = [];
      const mockTotal = 0;

      vi.mocked(outletRepository.findAll).mockResolvedValue(mockOutlets);
      vi.mocked(outletRepository.count).mockResolvedValue(mockTotal);

      await outletService.getAllOutlets(filters);

      expect(outletRepository.findAll).toHaveBeenCalledWith(filters);
      expect(outletRepository.count).toHaveBeenCalledWith({
        search: 'test',
        isActive: true,
        managerId: 1,
      });
    });

    it('should calculate total pages correctly', async () => {
      const mockOutlets = [];
      const mockTotal = 23;

      vi.mocked(outletRepository.findAll).mockResolvedValue(mockOutlets);
      vi.mocked(outletRepository.count).mockResolvedValue(mockTotal);

      const result = await outletService.getAllOutlets({ page: 1, limit: 10 });

      expect(result.totalPages).toBe(3); // Math.ceil(23 / 10)
    });
  });

  describe('getOutletById', () => {
    it('should return outlet with manager details', async () => {
      const mockOutlet = {
        outletId: 1,
        name: 'Test Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: true,
        manager: {
          userId: 1,
          fullName: 'John Doe',
          email: '<EMAIL>',
          role: 'admin',
        },
      };

      vi.mocked(outletRepository.findByIdWithManager).mockResolvedValue(mockOutlet);

      const result = await outletService.getOutletById(1);

      expect(outletRepository.findByIdWithManager).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockOutlet);
    });

    it('should return null for non-existent outlet', async () => {
      vi.mocked(outletRepository.findByIdWithManager).mockResolvedValue(null);

      const result = await outletService.getOutletById(999);

      expect(result).toBeNull();
    });
  });

  describe('createOutlet', () => {
    it('should create a new outlet', async () => {
      const createData: CreateOutletPayload = {
        name: 'New Outlet',
        address: '789 New St',
        phoneNumber: '+1111111111',
        email: '<EMAIL>',
        managerId: 1,
      };

      const mockCreatedOutlet = {
        outletId: 3,
        ...createData,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(outletRepository.create).mockResolvedValue(mockCreatedOutlet);

      const result = await outletService.createOutlet(createData);

      expect(outletRepository.create).toHaveBeenCalledWith(createData);
      expect(result).toEqual(mockCreatedOutlet);
    });

    it('should handle creation errors', async () => {
      const createData: CreateOutletPayload = {
        name: 'New Outlet',
        address: '789 New St',
        phoneNumber: '+1111111111',
      };

      const error = new Error('Database constraint violation');
      vi.mocked(outletRepository.create).mockRejectedValue(error);

      await expect(outletService.createOutlet(createData)).rejects.toThrow(error);
    });
  });

  describe('updateOutlet', () => {
    it('should update an existing outlet', async () => {
      const updateData: UpdateOutletPayload = {
        name: 'Updated Outlet',
        isActive: false,
      };

      const mockUpdatedOutlet = {
        outletId: 1,
        name: 'Updated Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: false,
        updatedAt: new Date(),
      };

      vi.mocked(outletRepository.update).mockResolvedValue(mockUpdatedOutlet);

      const result = await outletService.updateOutlet(1, updateData);

      expect(outletRepository.update).toHaveBeenCalledWith(1, updateData);
      expect(result).toEqual(mockUpdatedOutlet);
    });

    it('should return null for non-existent outlet', async () => {
      const updateData: UpdateOutletPayload = {
        name: 'Updated Outlet',
      };

      vi.mocked(outletRepository.update).mockResolvedValue(null);

      const result = await outletService.updateOutlet(999, updateData);

      expect(result).toBeNull();
    });
  });

  describe('deleteOutlet', () => {
    it('should soft delete an outlet', async () => {
      vi.mocked(outletRepository.delete).mockResolvedValue(true);

      const result = await outletService.deleteOutlet(1);

      expect(outletRepository.delete).toHaveBeenCalledWith(1);
      expect(result).toBe(true);
    });

    it('should return false for non-existent outlet', async () => {
      vi.mocked(outletRepository.delete).mockResolvedValue(false);

      const result = await outletService.deleteOutlet(999);

      expect(result).toBe(false);
    });
  });

  describe('restoreOutlet', () => {
    it('should restore a soft-deleted outlet', async () => {
      const mockRestoredOutlet = {
        outletId: 1,
        name: 'Restored Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: true,
        deletedAt: null,
      };

      vi.mocked(outletRepository.restore).mockResolvedValue(mockRestoredOutlet);

      const result = await outletService.restoreOutlet(1);

      expect(outletRepository.restore).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockRestoredOutlet);
    });
  });

  describe('bulkDeleteOutlets', () => {
    it('should delete multiple outlets', async () => {
      const outletIds = [1, 2, 3];
      const deletedCount = 3;

      vi.mocked(outletRepository.bulkDelete).mockResolvedValue(deletedCount);

      const result = await outletService.bulkDeleteOutlets(outletIds);

      expect(outletRepository.bulkDelete).toHaveBeenCalledWith(outletIds);
      expect(result).toBe(deletedCount);
    });

    it('should handle empty outlet IDs array', async () => {
      const result = await outletService.bulkDeleteOutlets([]);

      expect(outletRepository.bulkDelete).not.toHaveBeenCalled();
      expect(result).toBe(0);
    });
  });

  describe('activateOutlet', () => {
    it('should activate an outlet', async () => {
      const mockActivatedOutlet = {
        outletId: 1,
        name: 'Test Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: true,
      };

      vi.mocked(outletRepository.activate).mockResolvedValue(mockActivatedOutlet);

      const result = await outletService.activateOutlet(1);

      expect(outletRepository.activate).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockActivatedOutlet);
    });
  });

  describe('deactivateOutlet', () => {
    it('should deactivate an outlet', async () => {
      const mockDeactivatedOutlet = {
        outletId: 1,
        name: 'Test Outlet',
        address: '123 Test St',
        phoneNumber: '+1234567890',
        isActive: false,
      };

      vi.mocked(outletRepository.deactivate).mockResolvedValue(mockDeactivatedOutlet);

      const result = await outletService.deactivateOutlet(1);

      expect(outletRepository.deactivate).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockDeactivatedOutlet);
    });
  });

  describe('getOutletStatsSummary', () => {
    it('should return outlet statistics summary', async () => {
      const mockStats = {
        totalOutlets: 10,
        activeOutlets: 8,
        inactiveOutlets: 2,
        totalRevenue: 50000,
        averageRevenue: 5000,
      };

      vi.mocked(outletRepository.getStatsSummary).mockResolvedValue(mockStats);

      const result = await outletService.getOutletStatsSummary();

      expect(outletRepository.getStatsSummary).toHaveBeenCalled();
      expect(result).toEqual(mockStats);
    });
  });

  describe('searchOutlets', () => {
    it('should search outlets by query', async () => {
      const query = 'test outlet';
      const mockResults = [
        {
          outletId: 1,
          name: 'Test Outlet 1',
          address: '123 Test St',
          phoneNumber: '+1234567890',
          isActive: true,
        },
      ];

      vi.mocked(outletRepository.search).mockResolvedValue(mockResults);

      const result = await outletService.searchOutlets(query);

      expect(outletRepository.search).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResults);
    });

    it('should return empty array for empty query', async () => {
      const result = await outletService.searchOutlets('');

      expect(outletRepository.search).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });
});
