import { useState } from 'react';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  User,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  Activity,
} from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Card, CardContent } from '@/shared/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { OutletWithManager } from '../types';

interface OutletMobileCardProps {
  outlet: OutletWithManager;
  isSelected?: boolean;
  onSelect?: (selected: boolean) => void;
  onView?: (outlet: OutletWithManager) => void;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
  className?: string;
}

export function OutletMobileCard({
  outlet,
  isSelected = false,
  onSelect,
  onView,
  onEdit,
  onDelete,
  className,
}: OutletMobileCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleCardPress = () => {
    setIsExpanded(!isExpanded);
  };

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };

  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md active:scale-[0.98]',
        isSelected && 'ring-2 ring-primary ring-offset-2',
        className
      )}
    >
      <CardContent className="p-4">
        {/* Header Row */}
        <div className="flex items-start justify-between gap-3 mb-3">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            {onSelect && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={onSelect}
                className="mt-1 flex-shrink-0"
                onClick={(e) => e.stopPropagation()}
              />
            )}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <h3 
                  className="font-medium text-sm truncate cursor-pointer"
                  onClick={handleCardPress}
                >
                  {outlet.name}
                </h3>
                <Badge 
                  variant={outlet.isActive ? 'default' : 'secondary'}
                  className="text-xs flex-shrink-0"
                >
                  {outlet.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              
              {outlet.address && (
                <div className="flex items-start gap-2 text-xs text-muted-foreground">
                  <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  <span className="line-clamp-2">{outlet.address}</span>
                </div>
              )}
            </div>
          </div>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 flex-shrink-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {onView && (
                <DropdownMenuItem 
                  onClick={(e) => handleActionClick(e, () => onView(outlet))}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem 
                  onClick={(e) => handleActionClick(e, () => onEdit(outlet))}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Outlet
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  onClick={(e) => handleActionClick(e, () => onDelete(outlet))}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Outlet
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Quick Info Row */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
          {outlet.phoneNumber && (
            <div className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              <span className="truncate">{outlet.phoneNumber}</span>
            </div>
          )}
          
          {outlet.manager && (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span className="truncate">{outlet.manager.fullName}</span>
            </div>
          )}
        </div>

        {/* Expandable Details */}
        {isExpanded && (
          <div className="border-t pt-3 mt-3 space-y-2">
            {outlet.email && (
              <div className="flex items-center gap-2 text-xs">
                <Mail className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Email:</span>
                <span className="truncate">{outlet.email}</span>
              </div>
            )}
            
            {outlet.openingTime && outlet.closingTime && (
              <div className="flex items-center gap-2 text-xs">
                <Activity className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Hours:</span>
                <span>{outlet.openingTime} - {outlet.closingTime}</span>
              </div>
            )}
            
            {outlet.description && (
              <div className="text-xs">
                <span className="text-muted-foreground">Description:</span>
                <p className="mt-1 text-foreground line-clamp-3">{outlet.description}</p>
              </div>
            )}

            {outlet.manager && (
              <div className="flex items-center gap-2 text-xs">
                <User className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Manager:</span>
                <span>{outlet.manager.fullName}</span>
                <Badge variant="outline" className="text-xs">
                  {outlet.manager.role}
                </Badge>
              </div>
            )}

            {/* Action Buttons for Touch */}
            <div className="flex gap-2 pt-2">
              {onView && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-8 text-xs"
                  onClick={(e) => handleActionClick(e, () => onView(outlet))}
                >
                  <Eye className="mr-1 h-3 w-3" />
                  View
                </Button>
              )}
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-8 text-xs"
                  onClick={(e) => handleActionClick(e, () => onEdit(outlet))}
                >
                  <Edit className="mr-1 h-3 w-3" />
                  Edit
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-8 text-xs text-red-600 hover:text-red-600 hover:bg-red-50"
                  onClick={(e) => handleActionClick(e, () => onDelete(outlet))}
                >
                  <Trash2 className="mr-1 h-3 w-3" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Tap to expand hint */}
        {!isExpanded && (
          <div className="text-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs text-muted-foreground hover:text-foreground"
              onClick={handleCardPress}
            >
              Tap to expand
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
