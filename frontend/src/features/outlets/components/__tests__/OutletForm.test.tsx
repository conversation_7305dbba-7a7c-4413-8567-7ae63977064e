import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { OutletForm } from '../OutletForm';
import { CreateOutletRequest, UpdateOutletRequest, OutletWithManager } from '../../types';

// Mock the user queries hook
vi.mock('@/features/user-management/hooks/useUserQueries', () => ({
  useUsers: vi.fn(() => ({
    data: {
      users: [
        { userId: 1, fullName: '<PERSON>', role: 'admin' },
        { userId: 2, fullName: '<PERSON>', role: 'staff' },
        { userId: 3, fullName: '<PERSON>', role: 'user' },
      ],
    },
    isLoading: false,
    error: null,
  })),
}));

// Mock toast
vi.mock('@/shared/components/ui/use-toast', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

const mockOutlet: OutletWithManager = {
  id: 1,
  outletId: 1,
  name: 'Test Outlet',
  address: '123 Test Street',
  phone: '+**********',
  phoneNumber: '+**********',
  email: '<EMAIL>',
  managerId: 1,
  isActive: true,
  openingTime: '09:00',
  closingTime: '22:00',
  description: 'Test outlet description',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  manager: {
    userId: 1,
    fullName: 'John Doe',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
};

describe('OutletForm', () => {
  const mockOnSubmit = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Create Mode', () => {
    it('renders create form with empty fields', () => {
      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      expect(screen.getByLabelText(/outlet name/i)).toHaveValue('');
      expect(screen.getByLabelText(/phone number/i)).toHaveValue('');
      expect(screen.getByLabelText(/address/i)).toHaveValue('');
      expect(screen.getByRole('button', { name: /create outlet/i })).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      const submitButton = screen.getByRole('button', { name: /create outlet/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/outlet name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/phone number is required/i)).toBeInTheDocument();
        expect(screen.getByText(/address is required/i)).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('submits valid create form data', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(undefined);

      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      await user.type(screen.getByLabelText(/outlet name/i), 'New Outlet');
      await user.type(screen.getByLabelText(/phone number/i), '+**********');
      await user.type(screen.getByLabelText(/address/i), '123 New Street');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');

      const submitButton = screen.getByRole('button', { name: /create outlet/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'New Outlet',
          address: '123 New Street',
          phone: '+**********',
          email: '<EMAIL>',
          managerId: undefined,
        } as CreateOutletRequest);
      });
    });

    it('shows loading state during submission', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
          loading={true}
        />
      );

      expect(screen.getByRole('button', { name: /creating.../i })).toBeDisabled();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeDisabled();
    });
  });

  describe('Edit Mode', () => {
    it('renders edit form with pre-filled data', () => {
      renderWithQueryClient(
        <OutletForm
          outlet={mockOutlet}
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      expect(screen.getByDisplayValue('Test Outlet')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+**********')).toBeInTheDocument();
      expect(screen.getByDisplayValue('123 Test Street')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /update outlet/i })).toBeInTheDocument();
    });

    it('shows active status toggle in edit mode', () => {
      renderWithQueryClient(
        <OutletForm
          outlet={mockOutlet}
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      expect(screen.getByText(/active status/i)).toBeInTheDocument();
      expect(screen.getByRole('switch')).toBeChecked();
    });

    it('submits valid update form data', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(undefined);

      renderWithQueryClient(
        <OutletForm
          outlet={mockOutlet}
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      const nameInput = screen.getByDisplayValue('Test Outlet');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Outlet');

      const submitButton = screen.getByRole('button', { name: /update outlet/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'Updated Outlet',
          address: '123 Test Street',
          phone: '+**********',
          email: '<EMAIL>',
          managerId: '1',
          isActive: true,
        } as UpdateOutletRequest);
      });
    });

    it('handles form submission errors', async () => {
      const user = userEvent.setup();
      const error = new Error('Submission failed');
      mockOnSubmit.mockRejectedValue(error);

      renderWithQueryClient(
        <OutletForm
          outlet={mockOutlet}
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      const submitButton = screen.getByRole('button', { name: /update outlet/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/submission failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Manager Selection', () => {
    it('shows only staff and admin users in manager dropdown', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      const managerSelect = screen.getByRole('combobox', { name: /manager/i });
      await user.click(managerSelect);

      await waitFor(() => {
        expect(screen.getByText('John Doe (admin)')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith (staff)')).toBeInTheDocument();
        expect(screen.queryByText('Bob Johnson (user)')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Reset', () => {
    it('resets form when dialog opens/closes', () => {
      const { rerender } = renderWithQueryClient(
        <OutletForm
          isOpen={false}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      rerender(
        <OutletForm
          outlet={mockOutlet}
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      expect(screen.getByDisplayValue('Test Outlet')).toBeInTheDocument();
    });
  });

  describe('Cancel Button', () => {
    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletForm
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalled();
    });
  });
});
