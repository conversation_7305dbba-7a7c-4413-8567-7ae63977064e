import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { OutletDeleteDialog } from '../OutletDeleteDialog';
import { OutletWithManager } from '../../types';

const mockOutlet: OutletWithManager = {
  id: 1,
  outletId: 1,
  name: 'Test Outlet',
  address: '123 Test Street, Test City',
  phone: '+1234567890',
  phoneNumber: '+1234567890',
  email: '<EMAIL>',
  managerId: 1,
  isActive: true,
  openingTime: '09:00',
  closingTime: '22:00',
  description: 'Test outlet description',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  manager: {
    userId: 1,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
};

const mockOutletWithoutManager: OutletWithManager = {
  ...mockOutlet,
  managerId: undefined,
  manager: undefined,
};

const mockInactiveOutlet: OutletWithManager = {
  ...mockOutlet,
  isActive: false,
};

describe('OutletDeleteDialog', () => {
  const mockOnConfirm = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders nothing when outlet is null', () => {
      render(
        <OutletDeleteDialog
          outlet={null}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.queryByText(/delete outlet/i)).not.toBeInTheDocument();
    });

    it('renders dialog when outlet is provided and open is true', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText(/delete outlet/i)).toBeInTheDocument();
      expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument();
    });

    it('does not render dialog when open is false', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={false}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.queryByText(/delete outlet/i)).not.toBeInTheDocument();
    });
  });

  describe('Outlet Information Display', () => {
    it('displays outlet name and status', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Test Outlet')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });

    it('displays inactive status for inactive outlets', () => {
      render(
        <OutletDeleteDialog
          outlet={mockInactiveOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Inactive')).toBeInTheDocument();
    });

    it('displays outlet address when provided', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('123 Test Street, Test City')).toBeInTheDocument();
    });

    it('displays manager information when manager exists', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Manager: John Doe')).toBeInTheDocument();
    });

    it('does not display manager information when no manager assigned', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutletWithoutManager}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.queryByText(/manager:/i)).not.toBeInTheDocument();
    });
  });

  describe('Warning Information', () => {
    it('displays data loss warning', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText(/warning: data loss/i)).toBeInTheDocument();
      expect(screen.getByText(/all associated sales records/i)).toBeInTheDocument();
      expect(screen.getByText(/user assignments to this outlet/i)).toBeInTheDocument();
      expect(screen.getByText(/historical performance data/i)).toBeInTheDocument();
      expect(screen.getByText(/any related reports and analytics/i)).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalledTimes(1);
      expect(mockOnConfirm).not.toHaveBeenCalled();
    });

    it('calls onConfirm when delete button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      const deleteButton = screen.getByRole('button', { name: /delete outlet/i });
      await user.click(deleteButton);

      expect(mockOnConfirm).toHaveBeenCalledTimes(1);
      expect(mockOnCancel).not.toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('shows loading state when loading prop is true', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
          loading={true}
        />
      );

      expect(screen.getByText(/deleting.../i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /deleting.../i })).toBeDisabled();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeDisabled();
    });

    it('shows normal state when loading prop is false', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
          loading={false}
        />
      );

      expect(screen.getByRole('button', { name: /delete outlet/i })).not.toBeDisabled();
      expect(screen.getByRole('button', { name: /cancel/i })).not.toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete outlet/i })).toBeInTheDocument();
    });

    it('focuses on the dialog when opened', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      // The dialog should be focused or contain the focused element
      const dialog = screen.getByRole('dialog');
      expect(document.body).toContainElement(dialog);
    });
  });

  describe('Button Styling', () => {
    it('applies destructive styling to delete button', () => {
      render(
        <OutletDeleteDialog
          outlet={mockOutlet}
          open={true}
          onConfirm={mockOnConfirm}
          onCancel={mockOnCancel}
        />
      );

      const deleteButton = screen.getByRole('button', { name: /delete outlet/i });
      expect(deleteButton).toHaveClass('bg-red-600');
    });
  });
});
