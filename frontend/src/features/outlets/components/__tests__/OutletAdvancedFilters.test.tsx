import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { OutletAdvancedFilters } from '../OutletAdvancedFilters';
import { OutletFilters } from '../../types';

// Mock the user queries hook
vi.mock('@/features/user-management/hooks/useUserQueries', () => ({
  useUsers: vi.fn(() => ({
    data: {
      users: [
        { userId: 1, fullName: '<PERSON>', role: 'admin' },
        { userId: 2, fullName: '<PERSON>', role: 'staff' },
        { userId: 3, fullName: '<PERSON>', role: 'user' },
      ],
    },
    isLoading: false,
    error: null,
  })),
}));

// Mock date-fns format function
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') {
      return date.toISOString().split('T')[0];
    }
    return date.toLocaleDateString();
  }),
}));

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('OutletAdvancedFilters', () => {
  const mockOnFiltersChange = vi.fn();
  const defaultFilters: OutletFilters = {};

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders search input and status filter', () => {
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByPlaceholderText(/search outlets/i)).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: /status/i })).toBeInTheDocument();
    });

    it('renders advanced filters toggle button', () => {
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByRole('button', { name: /advanced filters/i })).toBeInTheDocument();
    });

    it('shows filter count badge when filters are active', () => {
      const filtersWithData: OutletFilters = {
        search: 'test',
        isActive: true,
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithData}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByText('2')).toBeInTheDocument(); // Badge showing count
    });
  });

  describe('Search Functionality', () => {
    it('calls onFiltersChange when search input changes', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const searchInput = screen.getByPlaceholderText(/search outlets/i);
      await user.type(searchInput, 'test outlet');

      await waitFor(() => {
        expect(mockOnFiltersChange).toHaveBeenCalledWith({
          search: 'test outlet',
        });
      });
    });

    it('displays current search value', () => {
      const filtersWithSearch: OutletFilters = {
        search: 'existing search',
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithSearch}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByDisplayValue('existing search')).toBeInTheDocument();
    });
  });

  describe('Status Filter', () => {
    it('calls onFiltersChange when status filter changes', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const activeOption = screen.getByText('Active');
      await user.click(activeOption);

      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        isActive: true,
      });
    });

    it('displays current status value', () => {
      const filtersWithStatus: OutletFilters = {
        isActive: true,
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithStatus}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      // The select should show "Active" as selected
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
  });

  describe('Advanced Filters Panel', () => {
    it('expands advanced filters when toggle is clicked', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const toggleButton = screen.getByRole('button', { name: /advanced filters/i });
      await user.click(toggleButton);

      await waitFor(() => {
        expect(screen.getByText(/manager/i)).toBeInTheDocument();
        expect(screen.getByText(/created from/i)).toBeInTheDocument();
        expect(screen.getByText(/created to/i)).toBeInTheDocument();
      });
    });

    it('shows manager filter options', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      // Open advanced filters
      const toggleButton = screen.getByRole('button', { name: /advanced filters/i });
      await user.click(toggleButton);

      await waitFor(() => {
        const managerSelect = screen.getByRole('combobox', { name: /manager/i });
        expect(managerSelect).toBeInTheDocument();
      });
    });
  });

  describe('Active Filter Badges', () => {
    it('displays badges for active filters', () => {
      const filtersWithData: OutletFilters = {
        search: 'test search',
        isActive: true,
        managerId: '1',
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithData}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByText(/search: "test search"/i)).toBeInTheDocument();
      expect(screen.getByText(/status: active/i)).toBeInTheDocument();
      expect(screen.getByText(/manager: john doe/i)).toBeInTheDocument();
    });

    it('allows removing individual filter badges', async () => {
      const user = userEvent.setup();
      const filtersWithData: OutletFilters = {
        search: 'test search',
        isActive: true,
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithData}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const searchBadge = screen.getByText(/search: "test search"/i);
      const removeButton = searchBadge.parentElement?.querySelector('button');
      
      if (removeButton) {
        await user.click(removeButton);
        expect(mockOnFiltersChange).toHaveBeenCalledWith({
          search: undefined,
          isActive: true,
        });
      }
    });
  });

  describe('Clear Filters', () => {
    it('shows clear button when filters are active', () => {
      const filtersWithData: OutletFilters = {
        search: 'test',
        isActive: true,
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithData}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.getByRole('button', { name: /clear/i })).toBeInTheDocument();
    });

    it('clears all filters when clear button is clicked', async () => {
      const user = userEvent.setup();
      const filtersWithData: OutletFilters = {
        search: 'test',
        isActive: true,
        managerId: '1',
      };

      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={filtersWithData}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const clearButton = screen.getByRole('button', { name: /clear/i });
      await user.click(clearButton);

      expect(mockOnFiltersChange).toHaveBeenCalledWith({});
    });

    it('does not show clear button when no filters are active', () => {
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      expect(screen.queryByRole('button', { name: /clear/i })).not.toBeInTheDocument();
    });
  });

  describe('Manager Filter', () => {
    it('shows only staff and admin users in manager dropdown', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      // Open advanced filters
      const toggleButton = screen.getByRole('button', { name: /advanced filters/i });
      await user.click(toggleButton);

      await waitFor(async () => {
        const managerSelect = screen.getByRole('combobox', { name: /manager/i });
        await user.click(managerSelect);
      });

      await waitFor(() => {
        expect(screen.getByText('John Doe (admin)')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith (staff)')).toBeInTheDocument();
        expect(screen.queryByText('Bob Johnson (user)')).not.toBeInTheDocument();
      });
    });
  });

  describe('Date Filters', () => {
    it('handles date from filter changes', async () => {
      const user = userEvent.setup();
      
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      // Open advanced filters
      const toggleButton = screen.getByRole('button', { name: /advanced filters/i });
      await user.click(toggleButton);

      await waitFor(() => {
        const dateFromButton = screen.getByRole('button', { name: /pick a date/i });
        expect(dateFromButton).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive classes', () => {
      renderWithQueryClient(
        <OutletAdvancedFilters
          filters={defaultFilters}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      const container = screen.getByPlaceholderText(/search outlets/i).closest('div');
      expect(container).toHaveClass('max-w-sm');
    });
  });
});
